"""
Excel reporting module for M&A research application.
Handles creation of Excel reports with proper formatting and line-break separated links.
"""

import pandas as pd
import logging
import os
from datetime import date
from typing import Dict, List, Tuple
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

class ExcelReporter:
    """Handles Excel report generation for M&A research results."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.output_dir = config.get('general_config', {}).get('output_directory', 'reports')
        self.logger = logging.getLogger(__name__)
        
        # Ensure output directory exists
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_report(self, company_summaries: Dict[str, str], 
                     company_links: Dict[str, List[str]],
                     company_names_original: Dict[str, str]) -> str:
        """
        Create Excel report with M&A research results.
        
        Args:
            company_summaries: Dict mapping company names to summaries
            company_links: Dict mapping company names to source URLs
            company_names_original: Dict mapping lowercase names to original capitalization
            
        Returns:
            Path to created Excel file
        """
        # Generate filename with date prefix
        today = date.today()
        filename = f"{today.isoformat()}-research.xlsx"
        filepath = os.path.join(self.output_dir, filename)
        
        # Prepare data for DataFrame
        report_data = []
        
        for company_lower, summary in company_summaries.items():
            # Get original capitalization
            original_name = company_names_original.get(company_lower, company_lower.title())
            
            # Get links for this company
            links = company_links.get(company_lower, [])
            
            # Format links with line breaks
            formatted_links = self._format_links_with_linebreaks(links)
            
            report_data.append({
                'Company Name': original_name,
                'Summary': summary,
                'Links': formatted_links,
                'Number of Sources': len(links),
                'Research Date': today.isoformat()
            })
        
        # Create DataFrame
        df = pd.DataFrame(report_data)
        
        # Sort by company name
        df = df.sort_values('Company Name')
        
        # Save to Excel with formatting
        self._save_formatted_excel(df, filepath)
        
        self.logger.info(f"Excel report created: {filepath}")
        return filepath
    
    def _format_links_with_linebreaks(self, links: List[str]) -> str:
        """
        Format links with line breaks for Excel display.
        
        Args:
            links: List of URLs
            
        Returns:
            String with URLs separated by line breaks
        """
        if not links:
            return "No sources found"
        
        # Remove duplicates while preserving order
        unique_links = []
        seen = set()
        for link in links:
            if link and link not in seen:
                unique_links.append(link)
                seen.add(link)
        
        # Join with line breaks
        return '\n'.join(unique_links)
    
    def _save_formatted_excel(self, df: pd.DataFrame, filepath: str):
        """
        Save DataFrame to Excel with proper formatting.
        
        Args:
            df: DataFrame to save
            filepath: Path to save Excel file
        """
        # Save basic Excel file first
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='M&A Research', index=False)
        
        # Load workbook for formatting
        wb = load_workbook(filepath)
        ws = wb.active
        
        # Apply formatting
        self._apply_excel_formatting(ws, len(df))
        
        # Save formatted workbook
        wb.save(filepath)
    
    def _apply_excel_formatting(self, worksheet, num_rows: int):
        """
        Apply formatting to Excel worksheet.
        
        Args:
            worksheet: openpyxl worksheet object
            num_rows: Number of data rows
        """
        # Define styles
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Format headers
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.border = border
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Set column widths
        column_widths = {
            'A': 25,  # Company Name
            'B': 60,  # Summary
            'C': 40,  # Links
            'D': 15,  # Number of Sources
            'E': 15   # Research Date
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # Format data rows
        for row_num in range(2, num_rows + 2):  # +2 because Excel is 1-indexed and we have headers
            for col_num in range(1, 6):  # 5 columns
                cell = worksheet.cell(row=row_num, column=col_num)
                cell.border = border
                
                # Special formatting for different columns
                if col_num == 1:  # Company Name
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='left', vertical='top')
                elif col_num == 2:  # Summary
                    cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                elif col_num == 3:  # Links
                    cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                    # Enable text wrapping for links column
                    worksheet.row_dimensions[row_num].height = None  # Auto-height
                elif col_num == 4:  # Number of Sources
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                elif col_num == 5:  # Research Date
                    cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # Auto-adjust row heights for wrapped text
        for row_num in range(2, num_rows + 2):
            # Calculate approximate height based on content
            summary_cell = worksheet.cell(row=row_num, column=2)
            links_cell = worksheet.cell(row=row_num, column=3)
            
            # Estimate height based on content length and wrapping
            summary_lines = max(1, len(str(summary_cell.value)) // 80)  # Approximate chars per line
            links_lines = str(links_cell.value).count('\n') + 1 if links_cell.value else 1
            
            estimated_height = max(summary_lines, links_lines) * 15  # 15 points per line
            worksheet.row_dimensions[row_num].height = max(30, min(estimated_height, 200))  # Min 30, max 200
        
        # Freeze header row
        worksheet.freeze_panes = 'A2'
        
        # Add auto-filter
        worksheet.auto_filter.ref = f"A1:E{num_rows + 1}"
    
    def create_summary_statistics(self, company_summaries: Dict[str, str],
                                company_links: Dict[str, List[str]]) -> Dict:
        """
        Create summary statistics for the report.
        
        Args:
            company_summaries: Dict mapping company names to summaries
            company_links: Dict mapping company names to source URLs
            
        Returns:
            Dictionary with summary statistics
        """
        total_companies = len(company_summaries)
        companies_with_news = sum(1 for summary in company_summaries.values() 
                                if "No M&A news found" not in summary)
        
        total_sources = sum(len(links) for links in company_links.values())
        
        # Count source types
        source_types = {}
        for links in company_links.values():
            for link in links:
                if 'youtube.com' in link or 'youtu.be' in link:
                    source_types['YouTube'] = source_types.get('YouTube', 0) + 1
                elif 'podcast' in link.lower():
                    source_types['Podcast'] = source_types.get('Podcast', 0) + 1
                else:
                    source_types['News Article'] = source_types.get('News Article', 0) + 1
        
        return {
            'total_companies_researched': total_companies,
            'companies_with_ma_news': companies_with_news,
            'companies_without_ma_news': total_companies - companies_with_news,
            'total_sources_found': total_sources,
            'source_breakdown': source_types,
            'research_date': date.today().isoformat()
        }
    
    def add_statistics_sheet(self, filepath: str, stats: Dict):
        """
        Add a statistics sheet to the Excel file.
        
        Args:
            filepath: Path to Excel file
            stats: Statistics dictionary
        """
        try:
            # Load existing workbook
            wb = load_workbook(filepath)
            
            # Create statistics sheet
            stats_ws = wb.create_sheet("Statistics")
            
            # Add statistics data
            stats_data = [
                ["M&A Research Statistics", ""],
                ["", ""],
                ["Total Companies Researched", stats['total_companies_researched']],
                ["Companies with M&A News", stats['companies_with_ma_news']],
                ["Companies without M&A News", stats['companies_without_ma_news']],
                ["Total Sources Found", stats['total_sources_found']],
                ["", ""],
                ["Source Breakdown", ""],
            ]
            
            # Add source breakdown
            for source_type, count in stats['source_breakdown'].items():
                stats_data.append([f"  {source_type}", count])
            
            stats_data.extend([
                ["", ""],
                ["Research Date", stats['research_date']]
            ])
            
            # Write data to sheet
            for row_num, (label, value) in enumerate(stats_data, 1):
                stats_ws.cell(row=row_num, column=1, value=label)
                stats_ws.cell(row=row_num, column=2, value=value)
            
            # Format statistics sheet
            stats_ws.column_dimensions['A'].width = 30
            stats_ws.column_dimensions['B'].width = 15
            
            # Bold headers
            stats_ws.cell(row=1, column=1).font = Font(bold=True, size=14)
            
            # Save workbook
            wb.save(filepath)
            
            self.logger.info("Added statistics sheet to Excel report")
            
        except Exception as e:
            self.logger.error(f"Failed to add statistics sheet: {e}")
    
    def validate_report(self, filepath: str) -> bool:
        """
        Validate that the Excel report was created correctly.
        
        Args:
            filepath: Path to Excel file
            
        Returns:
            True if report is valid
        """
        try:
            # Check if file exists
            if not os.path.exists(filepath):
                self.logger.error(f"Report file not found: {filepath}")
                return False
            
            # Try to read the file
            df = pd.read_excel(filepath, sheet_name='M&A Research')
            
            # Check required columns
            required_columns = ['Company Name', 'Summary', 'Links']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.error(f"Missing required column: {col}")
                    return False
            
            # Check if we have data
            if len(df) == 0:
                self.logger.error("Report contains no data")
                return False
            
            self.logger.info(f"Report validation successful: {len(df)} companies")
            return True
            
        except Exception as e:
            self.logger.error(f"Report validation failed: {e}")
            return False
