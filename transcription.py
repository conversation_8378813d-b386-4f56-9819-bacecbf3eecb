"""
Transcription module for M&A research application.
Handles audio transcription from videos and podcasts using AssemblyAI or local Whisper.
"""

import requests
import logging
import time
import os
import tempfile
from typing import Dict, Optional, List
import json
from urllib.parse import urlparse

try:
    import assemblyai as aai
    ASSEMBLYAI_AVAILABLE = True
except ImportError:
    ASSEMBLYAI_AVAILABLE = False
    logging.warning("AssemblyAI not available, will use fallback methods")

try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    logging.warning("Whisper not available, transcription may be limited")

try:
    from youtube_transcript_api import YouTubeTranscriptApi
    YOUTUBE_TRANSCRIPT_AVAILABLE = True
except ImportError:
    YOUTUBE_TRANSCRIPT_AVAILABLE = False
    logging.warning("YouTube Transcript API not available")

class AssemblyAITranscriber:
    """Transcriber using AssemblyAI service."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.api_key = config['api_key']
        self.endpoint = config['endpoint']
        self.logger = logging.getLogger(__name__)
        
        if ASSEMBLYAI_AVAILABLE and self.api_key != 'your_assemblyai_key_here':
            aai.settings.api_key = self.api_key
        else:
            self.logger.warning("AssemblyAI not properly configured")
    
    def transcribe_audio_url(self, audio_url: str) -> Optional[str]:
        """
        Transcribe audio from URL using AssemblyAI.
        
        Args:
            audio_url: URL of audio file to transcribe
            
        Returns:
            Transcribed text or None if failed
        """
        if not ASSEMBLYAI_AVAILABLE or self.api_key == 'your_assemblyai_key_here':
            return None
        
        try:
            # Configure transcription settings
            config = aai.TranscriptionConfig(
                language_code=self.config.get('language', 'en'),
                punctuate=True,
                format_text=True
            )
            
            # Create transcriber and transcribe
            transcriber = aai.Transcriber(config=config)
            transcript = transcriber.transcribe(audio_url)
            
            if transcript.status == aai.TranscriptStatus.error:
                self.logger.error(f"AssemblyAI transcription failed: {transcript.error}")
                return None
            
            self.logger.info(f"Successfully transcribed audio from {audio_url}")
            return transcript.text
            
        except Exception as e:
            self.logger.error(f"AssemblyAI transcription error: {e}")
            return None
    
    def transcribe_local_file(self, file_path: str) -> Optional[str]:
        """
        Transcribe local audio file using AssemblyAI.
        
        Args:
            file_path: Path to local audio file
            
        Returns:
            Transcribed text or None if failed
        """
        if not ASSEMBLYAI_AVAILABLE or self.api_key == 'your_assemblyai_key_here':
            return None
        
        try:
            # Upload file first
            transcriber = aai.Transcriber()
            transcript = transcriber.transcribe(file_path)
            
            if transcript.status == aai.TranscriptStatus.error:
                self.logger.error(f"AssemblyAI transcription failed: {transcript.error}")
                return None
            
            self.logger.info(f"Successfully transcribed local file {file_path}")
            return transcript.text
            
        except Exception as e:
            self.logger.error(f"AssemblyAI local transcription error: {e}")
            return None

class WhisperTranscriber:
    """Transcriber using local Whisper model."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.model = None
        
        if WHISPER_AVAILABLE:
            try:
                # Load smaller model for Raspberry Pi
                self.model = whisper.load_model("base")
                self.logger.info("Whisper model loaded successfully")
            except Exception as e:
                self.logger.error(f"Failed to load Whisper model: {e}")
    
    def transcribe_local_file(self, file_path: str) -> Optional[str]:
        """
        Transcribe local audio file using Whisper.
        
        Args:
            file_path: Path to local audio file
            
        Returns:
            Transcribed text or None if failed
        """
        if not self.model:
            return None
        
        try:
            result = self.model.transcribe(file_path)
            self.logger.info(f"Successfully transcribed {file_path} with Whisper")
            return result["text"]
            
        except Exception as e:
            self.logger.error(f"Whisper transcription error: {e}")
            return None

class YouTubeTranscriptExtractor:
    """Extracts existing transcripts from YouTube videos."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_transcript(self, video_id: str) -> Optional[str]:
        """
        Get transcript for YouTube video if available.
        
        Args:
            video_id: YouTube video ID
            
        Returns:
            Transcript text or None if not available
        """
        if not YOUTUBE_TRANSCRIPT_AVAILABLE:
            return None
        
        try:
            # Try to get transcript in English
            transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])
            
            # Combine all transcript segments
            full_transcript = ' '.join([entry['text'] for entry in transcript_list])
            
            self.logger.info(f"Successfully extracted transcript for video {video_id}")
            return full_transcript
            
        except Exception as e:
            self.logger.debug(f"No transcript available for video {video_id}: {e}")
            return None

class AudioDownloader:
    """Downloads audio from video URLs for transcription."""
    
    def __init__(self, temp_dir: str = None):
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.logger = logging.getLogger(__name__)
    
    def download_youtube_audio(self, video_url: str) -> Optional[str]:
        """
        Download audio from YouTube video.
        
        Args:
            video_url: YouTube video URL
            
        Returns:
            Path to downloaded audio file or None if failed
        """
        try:
            # This is a simplified implementation
            # In production, you'd use yt-dlp or similar
            self.logger.warning("Audio download not implemented - would need yt-dlp")
            return None
            
        except Exception as e:
            self.logger.error(f"Audio download error: {e}")
            return None

class TranscriptionManager:
    """Manages all transcription services."""
    
    def __init__(self, config: Dict):
        self.config = config['transcription_config']
        self.logger = logging.getLogger(__name__)
        
        # Initialize transcription services
        self.assemblyai = AssemblyAITranscriber(self.config)
        self.whisper = WhisperTranscriber(self.config)
        self.youtube_transcript = YouTubeTranscriptExtractor()
        self.audio_downloader = AudioDownloader(
            config.get('general_config', {}).get('temp_directory', 'temp')
        )
    
    def transcribe_content(self, content_item: Dict) -> Optional[str]:
        """
        Transcribe content based on its type.
        
        Args:
            content_item: Content item dictionary with type and URL
            
        Returns:
            Transcribed text or None if failed
        """
        source_type = content_item.get('source_type', '')
        
        if source_type == 'youtube_video':
            return self._transcribe_youtube_video(content_item)
        elif source_type == 'podcast_episode':
            return self._transcribe_podcast(content_item)
        else:
            self.logger.debug(f"No transcription needed for {source_type}")
            return None
    
    def _transcribe_youtube_video(self, video_item: Dict) -> Optional[str]:
        """Transcribe YouTube video using best available method."""
        video_id = video_item.get('video_id', '')
        video_url = video_item.get('url', '')
        
        if not video_id and video_url:
            # Extract video ID from URL
            if 'youtube.com/watch?v=' in video_url:
                video_id = video_url.split('watch?v=')[1].split('&')[0]
            elif 'youtu.be/' in video_url:
                video_id = video_url.split('youtu.be/')[1].split('?')[0]
        
        # Try YouTube transcript API first (fastest)
        if video_id:
            transcript = self.youtube_transcript.get_transcript(video_id)
            if transcript:
                return transcript
        
        # If no existing transcript, try downloading and transcribing
        # This would require additional implementation for audio extraction
        self.logger.info(f"No existing transcript for video {video_id}, would need audio extraction")
        return None
    
    def _transcribe_podcast(self, podcast_item: Dict) -> Optional[str]:
        """Transcribe podcast episode."""
        # This would require downloading the podcast audio file
        # and then transcribing it - simplified for now
        self.logger.info("Podcast transcription would require audio file download")
        return None
    
    def filter_relevant_transcript(self, transcript: str, company_names: List[str]) -> Optional[str]:
        """
        Filter transcript to only include relevant sections mentioning companies.
        
        Args:
            transcript: Full transcript text
            company_names: List of company names to look for
            
        Returns:
            Filtered transcript or None if no mentions found
        """
        if not transcript:
            return None
        
        # Split transcript into sentences
        sentences = transcript.split('. ')
        relevant_sentences = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            
            # Check if sentence mentions any company
            for company in company_names:
                if company.lower() in sentence_lower:
                    # Also check for M&A keywords in the sentence or nearby context
                    ma_keywords = ['merger', 'acquisition', 'acquire', 'merge', 'buyout', 
                                  'takeover', 'investment', 'deal', 'purchase']
                    
                    # Get some context around the sentence
                    sentence_index = sentences.index(sentence)
                    context_start = max(0, sentence_index - 2)
                    context_end = min(len(sentences), sentence_index + 3)
                    context = ' '.join(sentences[context_start:context_end]).lower()
                    
                    if any(keyword in context for keyword in ma_keywords):
                        relevant_sentences.extend(sentences[context_start:context_end])
                        break
        
        if relevant_sentences:
            # Remove duplicates while preserving order
            seen = set()
            unique_sentences = []
            for sentence in relevant_sentences:
                if sentence not in seen:
                    seen.add(sentence)
                    unique_sentences.append(sentence)
            
            filtered_transcript = '. '.join(unique_sentences)
            self.logger.info(f"Filtered transcript to {len(unique_sentences)} relevant sentences")
            return filtered_transcript
        
        return None
