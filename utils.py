"""
Utility functions for M&A research application.
Handles case-insensitive processing, date calculations, and other common operations.
"""

import re
import logging
from datetime import datetime, date, timedelta
from typing import List, Dict, Optional, Tuple
import hashlib
import json

class CompanyNameProcessor:
    """Handles case-insensitive company name processing."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def normalize_company_names(self, companies: List[str]) -> Dict[str, str]:
        """
        Create a mapping of normalized (lowercase) names to original names.
        
        Args:
            companies: List of company names as provided by user
            
        Returns:
            Dict mapping lowercase names to original capitalization
        """
        normalized_map = {}
        for company in companies:
            cleaned = company.strip()
            if cleaned:
                normalized_map[cleaned.lower()] = cleaned
        
        self.logger.info(f"Normalized {len(normalized_map)} company names")
        return normalized_map
    
    def get_search_variations(self, company_name: str) -> List[str]:
        """
        Generate search variations for a company name.
        
        Args:
            company_name: Original company name
            
        Returns:
            List of search variations
        """
        variations = [company_name]
        
        # Add common variations
        if ' ' in company_name:
            # Add version without spaces
            variations.append(company_name.replace(' ', ''))
            
            # Add version with different separators
            variations.append(company_name.replace(' ', '-'))
            variations.append(company_name.replace(' ', '_'))
        
        # Add common corporate suffixes/prefixes variations
        suffixes_to_try = ['Inc', 'Corp', 'LLC', 'Ltd', 'Co', 'Company']
        base_name = company_name
        
        # Remove existing suffixes
        for suffix in suffixes_to_try:
            patterns = [f' {suffix}', f' {suffix}.', f', {suffix}', f', {suffix}.']
            for pattern in patterns:
                if base_name.endswith(pattern):
                    base_name = base_name[:-len(pattern)]
                    break
        
        # Add the base name without suffixes
        if base_name != company_name:
            variations.append(base_name)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_variations = []
        for var in variations:
            if var.lower() not in seen:
                seen.add(var.lower())
                unique_variations.append(var)
        
        return unique_variations

class DateCalculator:
    """Handles date calculations for differential reporting."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_search_date_range(self, last_run_date: Optional[date], 
                                  initial_lookback_days: int = 7) -> Tuple[date, date]:
        """
        Calculate the date range for searching new content.
        
        Args:
            last_run_date: Date of last successful run (None for first run)
            initial_lookback_days: Days to look back for initial run
            
        Returns:
            Tuple of (start_date, end_date) for search
        """
        end_date = date.today()
        
        if last_run_date is None:
            # First run - look back specified days
            start_date = end_date - timedelta(days=initial_lookback_days)
            self.logger.info(f"First run: searching from {start_date} to {end_date}")
        else:
            # Subsequent run - search from day after last run
            start_date = last_run_date + timedelta(days=1)
            self.logger.info(f"Subsequent run: searching from {start_date} to {end_date}")
        
        return start_date, end_date
    
    def format_date_for_api(self, date_obj: date, api_format: str = "iso") -> str:
        """
        Format date for different API requirements.
        
        Args:
            date_obj: Date to format
            api_format: Format type ('iso', 'newsapi', 'serpapi')
            
        Returns:
            Formatted date string
        """
        if api_format == "iso":
            return date_obj.isoformat()
        elif api_format == "newsapi":
            return date_obj.strftime("%Y-%m-%d")
        elif api_format == "serpapi":
            return date_obj.strftime("%m/%d/%Y")
        else:
            return date_obj.isoformat()

class ContentHasher:
    """Handles content hashing for duplicate detection."""
    
    @staticmethod
    def hash_content(content: str, additional_data: str = "") -> str:
        """
        Generate a consistent hash for content.
        
        Args:
            content: Main content to hash
            additional_data: Additional data to include in hash
            
        Returns:
            SHA256 hash string
        """
        combined = f"{content.strip()}{additional_data}"
        return hashlib.sha256(combined.encode('utf-8')).hexdigest()
    
    @staticmethod
    def hash_article(title: str, content: str, url: str = "") -> str:
        """Hash an article for duplicate detection."""
        return ContentHasher.hash_content(f"{title}{content}", url)
    
    @staticmethod
    def hash_transcript(transcript: str, video_id: str = "") -> str:
        """Hash a transcript for duplicate detection."""
        return ContentHasher.hash_content(transcript, video_id)

class TextCleaner:
    """Handles text cleaning and preprocessing."""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        Clean and normalize text content.
        
        Args:
            text: Raw text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove common HTML entities
        html_entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&nbsp;': ' '
        }
        
        for entity, replacement in html_entities.items():
            text = text.replace(entity, replacement)
        
        return text
    
    @staticmethod
    def extract_company_mentions(text: str, company_names: List[str]) -> List[str]:
        """
        Find mentions of company names in text (case-insensitive).
        
        Args:
            text: Text to search
            company_names: List of company names to look for
            
        Returns:
            List of found company names (original capitalization)
        """
        found_companies = []
        text_lower = text.lower()
        
        for company in company_names:
            # Create regex pattern for whole word matching
            pattern = r'\b' + re.escape(company.lower()) + r'\b'
            if re.search(pattern, text_lower):
                found_companies.append(company)
        
        return found_companies

class ConfigValidator:
    """Validates configuration settings."""
    
    @staticmethod
    def validate_config(config: Dict) -> Tuple[bool, List[str]]:
        """
        Validate configuration dictionary.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Check required sections
        required_sections = [
            'data_source_config',
            'llm_config', 
            'transcription_config',
            'email_config'
        ]
        
        for section in required_sections:
            if section not in config:
                errors.append(f"Missing required section: {section}")
        
        # Validate LLM config
        if 'llm_config' in config:
            llm_config = config['llm_config']
            if not llm_config.get('api_key') or llm_config['api_key'] == 'your_google_ai_api_key_here':
                errors.append("LLM API key not configured")
        
        # Validate email config
        if 'email_config' in config:
            email_config = config['email_config']
            required_email_fields = ['smtp_server', 'sender_email', 'sender_password']
            for field in required_email_fields:
                if not email_config.get(field):
                    errors.append(f"Email config missing: {field}")
        
        return len(errors) == 0, errors

def setup_logging(config: Dict) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured logger
    """
    log_config = config.get('logging_config', {})
    
    logging.basicConfig(
        level=getattr(logging, log_config.get('level', 'INFO')),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_config.get('log_file', 'ma_research.log')),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('ma_research')

def create_directories(config: Dict) -> bool:
    """
    Create necessary directories.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        True if successful
    """
    import os
    
    directories = [
        config.get('general_config', {}).get('output_directory', 'reports'),
        config.get('general_config', {}).get('temp_directory', 'temp')
    ]
    
    try:
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Error creating directories: {e}")
        return False
