{"data_source_config": {"news_api": {"provider": "newsapi", "api_key": "your_newsapi_key_here", "endpoint": "https://newsapi.org/v2/everything", "max_articles": 50}, "serpapi": {"api_key": "your_serpapi_key_here", "endpoint": "https://serpapi.com/search", "max_results": 20}, "multimedia_sources": {"youtube": {"enabled": true, "max_videos": 10}, "podcasts": {"enabled": true, "max_episodes": 5}}}, "llm_config": {"provider": "google_generative_ai", "api_key": "your_google_ai_api_key_here", "endpoint": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent", "model": "gemini-2.5-flash-preview-05-20", "summarization_prompt": "Summarize the following text, focusing strictly on merger, acquisition, or investment news for the specified company. Identify the companies involved and the nature of the deal. If no such news is present, state 'No M&A news found'. Summary: ", "max_tokens": 1000, "temperature": 0.3}, "transcription_config": {"provider": "assemblyai", "api_key": "your_assemblyai_key_here", "endpoint": "https://api.assemblyai.com/v2/transcript", "fallback_provider": "whisper_local", "language": "en"}, "email_config": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "sender_email": "<EMAIL>", "sender_password": "your_app_password_here", "use_tls": true, "subject_template": "M&A Research Report - {date}", "body_template": "Your M&A research for {date} is complete. The report is attached.\n\nCompanies researched: {companies}\n\nBest regards,\nM&A Research Bot"}, "scheduler_config": {"enabled": false, "schedule_type": "daily", "run_time": "09:00", "days_of_week": ["monday", "wednesday", "friday"], "timezone": "UTC"}, "database_config": {"db_path": "ma_research.db", "backup_enabled": true, "cleanup_days": 90}, "logging_config": {"level": "INFO", "log_file": "ma_research.log", "max_file_size": "10MB", "backup_count": 5}, "general_config": {"initial_lookback_days": 7, "output_directory": "reports", "temp_directory": "temp", "max_concurrent_requests": 5, "request_delay_seconds": 1}}