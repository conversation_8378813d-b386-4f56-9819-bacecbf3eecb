"""
LLM integration module for M&A research application.
Handles Google Generative AI integration for content summarization.
"""

import logging
import time
from typing import List, Dict, Optional
import json

try:
    import google.generativeai as genai
    GOOGLE_AI_AVAILABLE = True
except ImportError:
    GOOGLE_AI_AVAILABLE = False
    logging.warning("Google Generative AI not available")

class GoogleGenerativeAISummarizer:
    """Summarizer using Google Generative AI (Gemini)."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.api_key = config['api_key']
        self.model_name = config['model']
        self.endpoint = config['endpoint']
        self.summarization_prompt = config['summarization_prompt']
        self.max_tokens = config.get('max_tokens', 1000)
        self.temperature = config.get('temperature', 0.3)
        self.logger = logging.getLogger(__name__)
        
        # Initialize the client
        if GOOGLE_AI_AVAILABLE and self.api_key != 'your_google_ai_api_key_here':
            try:
                genai.configure(api_key=self.api_key)
                self.model = genai.GenerativeModel(self.model_name)
                self.logger.info(f"Google Generative AI initialized with model {self.model_name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize Google Generative AI: {e}")
                self.model = None
        else:
            self.model = None
            self.logger.warning("Google Generative AI not properly configured")
    
    def summarize_content(self, content_items: List[Dict], company_name: str) -> Optional[str]:
        """
        Summarize content items for a specific company.
        
        Args:
            content_items: List of content items (articles, transcripts, etc.)
            company_name: Name of the company being researched
            
        Returns:
            Summarized text or None if failed
        """
        if not self.model:
            self.logger.error("Google Generative AI model not available")
            return None
        
        if not content_items:
            return "No M&A news found."
        
        # Prepare content for summarization
        combined_content = self._prepare_content_for_summarization(content_items, company_name)
        
        if not combined_content.strip():
            return "No M&A news found."
        
        # Create the prompt
        full_prompt = f"""
{self.summarization_prompt}

Company: {company_name}

Content to summarize:
{combined_content}

Please provide a comprehensive summary focusing on merger, acquisition, or investment news related to {company_name}. Include:
1. Key deals or transactions mentioned
2. Companies involved
3. Deal values if mentioned
4. Current status of any deals
5. Strategic implications

If no M&A-related news is found, respond with "No M&A news found."
"""
        
        try:
            # Generate summary
            response = self.model.generate_content(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=self.temperature,
                )
            )
            
            if response.text:
                summary = response.text.strip()
                self.logger.info(f"Successfully generated summary for {company_name}")
                return summary
            else:
                self.logger.warning(f"Empty response from Google AI for {company_name}")
                return "No M&A news found."
                
        except Exception as e:
            self.logger.error(f"Google Generative AI summarization error: {e}")
            return None
    
    def _prepare_content_for_summarization(self, content_items: List[Dict], 
                                         company_name: str) -> str:
        """
        Prepare and combine content items for summarization.
        
        Args:
            content_items: List of content items
            company_name: Company name for filtering
            
        Returns:
            Combined content string
        """
        combined_parts = []
        
        for i, item in enumerate(content_items, 1):
            source_type = item.get('source_type', 'unknown')
            title = item.get('title', 'No title')
            content = item.get('content', '')
            url = item.get('url', '')
            
            # For transcripts, use the transcript text
            if 'transcript' in item:
                content = item['transcript']
            
            # Skip if no meaningful content
            if not content or len(content.strip()) < 50:
                continue
            
            # Truncate very long content to avoid token limits
            if len(content) > 2000:
                content = content[:2000] + "..."
            
            part = f"""
--- Source {i} ({source_type}) ---
Title: {title}
URL: {url}
Content: {content}
"""
            combined_parts.append(part)
        
        return "\n".join(combined_parts)
    
    def batch_summarize(self, company_content_map: Dict[str, List[Dict]]) -> Dict[str, str]:
        """
        Summarize content for multiple companies.
        
        Args:
            company_content_map: Dict mapping company names to their content items
            
        Returns:
            Dict mapping company names to their summaries
        """
        summaries = {}
        
        for company_name, content_items in company_content_map.items():
            self.logger.info(f"Generating summary for {company_name}")
            
            summary = self.summarize_content(content_items, company_name)
            if summary:
                summaries[company_name] = summary
            else:
                summaries[company_name] = "Failed to generate summary."
            
            # Rate limiting to avoid API limits
            time.sleep(1)
        
        return summaries

class FallbackSummarizer:
    """Fallback summarizer for when LLM services are unavailable."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def summarize_content(self, content_items: List[Dict], company_name: str) -> str:
        """
        Create a basic summary without LLM.
        
        Args:
            content_items: List of content items
            company_name: Company name
            
        Returns:
            Basic summary string
        """
        if not content_items:
            return "No M&A news found."
        
        # Extract key information
        titles = []
        sources = []
        
        for item in content_items:
            title = item.get('title', '')
            source_type = item.get('source_type', 'unknown')
            
            if title:
                titles.append(title)
            sources.append(source_type)
        
        # Create basic summary
        summary_parts = [
            f"Found {len(content_items)} items related to {company_name}:",
        ]
        
        if titles:
            summary_parts.append("Key headlines:")
            for i, title in enumerate(titles[:5], 1):  # Limit to top 5
                summary_parts.append(f"{i}. {title}")
        
        source_counts = {}
        for source in sources:
            source_counts[source] = source_counts.get(source, 0) + 1
        
        if source_counts:
            summary_parts.append(f"Sources: {', '.join([f'{count} {source}' for source, count in source_counts.items()])}")
        
        return "\n".join(summary_parts)

class LLMManager:
    """Manages LLM integration for summarization."""
    
    def __init__(self, config: Dict):
        self.config = config['llm_config']
        self.logger = logging.getLogger(__name__)
        
        # Initialize primary summarizer
        provider = self.config.get('provider', 'google_generative_ai')
        
        if provider == 'google_generative_ai':
            self.primary_summarizer = GoogleGenerativeAISummarizer(self.config)
        else:
            self.logger.warning(f"Unknown LLM provider: {provider}")
            self.primary_summarizer = None
        
        # Always have fallback available
        self.fallback_summarizer = FallbackSummarizer()
    
    def generate_summaries(self, company_content_map: Dict[str, List[Dict]]) -> Dict[str, str]:
        """
        Generate summaries for all companies.
        
        Args:
            company_content_map: Dict mapping company names to content items
            
        Returns:
            Dict mapping company names to summaries
        """
        summaries = {}
        
        # Try primary summarizer first
        if self.primary_summarizer and hasattr(self.primary_summarizer, 'model') and self.primary_summarizer.model:
            try:
                summaries = self.primary_summarizer.batch_summarize(company_content_map)
                self.logger.info("Successfully generated summaries using primary LLM")
                return summaries
            except Exception as e:
                self.logger.error(f"Primary LLM failed: {e}")
        
        # Fall back to basic summarization
        self.logger.warning("Using fallback summarizer")
        for company_name, content_items in company_content_map.items():
            summaries[company_name] = self.fallback_summarizer.summarize_content(
                content_items, company_name
            )
        
        return summaries
    
    def test_connection(self) -> bool:
        """
        Test LLM connection.
        
        Returns:
            True if connection successful
        """
        if not self.primary_summarizer:
            return False
        
        try:
            # Test with simple content
            test_content = [{
                'title': 'Test Article',
                'content': 'This is a test article about company mergers.',
                'source_type': 'test'
            }]
            
            result = self.primary_summarizer.summarize_content(test_content, "TestCompany")
            return result is not None
            
        except Exception as e:
            self.logger.error(f"LLM connection test failed: {e}")
            return False
