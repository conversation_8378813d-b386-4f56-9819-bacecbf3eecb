"""
Email notification module for M&A research application.
Handles SMTP email sending with Excel report attachments.
"""

import smtplib
import logging
import os
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import date
from typing import Dict, List, Optional

class EmailNotifier:
    """Handles email notifications with Excel report attachments."""
    
    def __init__(self, config: Dict):
        self.config = config['email_config']
        self.smtp_server = self.config['smtp_server']
        self.smtp_port = self.config['smtp_port']
        self.sender_email = self.config['sender_email']
        self.sender_password = self.config['sender_password']
        self.use_tls = self.config.get('use_tls', True)
        self.subject_template = self.config.get('subject_template', 'M&A Research Report - {date}')
        self.body_template = self.config.get('body_template', 
            'Your M&A research for {date} is complete. The report is attached.\n\nCompanies researched: {companies}\n\nBest regards,\nM&A Research Bot')
        
        self.logger = logging.getLogger(__name__)
    
    def send_report(self, recipient_email: str, excel_file_path: str, 
                   companies: List[str], stats: Optional[Dict] = None) -> bool:
        """
        Send M&A research report via email.
        
        Args:
            recipient_email: Email address to send report to
            excel_file_path: Path to Excel report file
            companies: List of companies researched
            stats: Optional statistics dictionary
            
        Returns:
            True if email sent successfully
        """
        try:
            # Validate inputs
            if not self._validate_email_config():
                return False
            
            if not os.path.exists(excel_file_path):
                self.logger.error(f"Excel file not found: {excel_file_path}")
                return False
            
            # Create email message
            msg = self._create_email_message(recipient_email, companies, stats)
            
            # Attach Excel file
            if not self._attach_excel_file(msg, excel_file_path):
                return False
            
            # Send email
            return self._send_email(msg, recipient_email)
            
        except Exception as e:
            self.logger.error(f"Failed to send email: {e}")
            return False
    
    def _validate_email_config(self) -> bool:
        """Validate email configuration."""
        required_fields = ['smtp_server', 'sender_email', 'sender_password']
        
        for field in required_fields:
            if not self.config.get(field):
                self.logger.error(f"Email configuration missing: {field}")
                return False
        
        # Check for placeholder values
        if self.sender_password in ['your_email_password_or_app_token', 'your_app_password_here']:
            self.logger.error("Email password not configured (still using placeholder)")
            return False
        
        return True
    
    def _create_email_message(self, recipient_email: str, companies: List[str], 
                            stats: Optional[Dict] = None) -> MIMEMultipart:
        """Create email message with proper headers and body."""
        msg = MIMEMultipart()
        
        # Set headers
        today = date.today().isoformat()
        msg['From'] = self.sender_email
        msg['To'] = recipient_email
        msg['Subject'] = self.subject_template.format(date=today)
        
        # Create email body
        companies_str = ', '.join(companies)
        body_text = self.body_template.format(
            date=today,
            companies=companies_str
        )
        
        # Add statistics if available
        if stats:
            body_text += self._format_stats_for_email(stats)
        
        # Attach body
        msg.attach(MIMEText(body_text, 'plain'))
        
        return msg
    
    def _format_stats_for_email(self, stats: Dict) -> str:
        """Format statistics for email body."""
        stats_text = f"""

Research Summary:
- Total companies researched: {stats.get('total_companies_researched', 0)}
- Companies with M&A news: {stats.get('companies_with_ma_news', 0)}
- Companies without M&A news: {stats.get('companies_without_ma_news', 0)}
- Total sources found: {stats.get('total_sources_found', 0)}

Source breakdown:"""
        
        source_breakdown = stats.get('source_breakdown', {})
        for source_type, count in source_breakdown.items():
            stats_text += f"\n- {source_type}: {count}"
        
        return stats_text
    
    def _attach_excel_file(self, msg: MIMEMultipart, excel_file_path: str) -> bool:
        """Attach Excel file to email message."""
        try:
            with open(excel_file_path, "rb") as attachment:
                # Create MIMEBase object
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            # Encode file in ASCII characters to send by email
            encoders.encode_base64(part)
            
            # Add header with filename
            filename = os.path.basename(excel_file_path)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            # Attach the part to message
            msg.attach(part)
            
            self.logger.info(f"Attached Excel file: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to attach Excel file: {e}")
            return False
    
    def _send_email(self, msg: MIMEMultipart, recipient_email: str) -> bool:
        """Send email via SMTP."""
        try:
            # Create SMTP session
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            # Enable TLS if configured
            if self.use_tls:
                server.starttls()
            
            # Login with sender's email and password
            server.login(self.sender_email, self.sender_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.sender_email, recipient_email, text)
            server.quit()
            
            self.logger.info(f"Email sent successfully to {recipient_email}")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            self.logger.error(f"SMTP authentication failed: {e}")
            self.logger.error("Check your email credentials and app password settings")
            return False
        except smtplib.SMTPRecipientsRefused as e:
            self.logger.error(f"Recipient email refused: {e}")
            return False
        except smtplib.SMTPServerDisconnected as e:
            self.logger.error(f"SMTP server disconnected: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Failed to send email: {e}")
            return False
    
    def test_email_connection(self) -> bool:
        """
        Test email server connection and authentication.
        
        Returns:
            True if connection successful
        """
        try:
            if not self._validate_email_config():
                return False
            
            # Test SMTP connection
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.use_tls:
                server.starttls()
            
            # Test authentication
            server.login(self.sender_email, self.sender_password)
            server.quit()
            
            self.logger.info("Email connection test successful")
            return True
            
        except smtplib.SMTPAuthenticationError as e:
            self.logger.error(f"Email authentication test failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Email connection test failed: {e}")
            return False
    
    def send_test_email(self, recipient_email: str) -> bool:
        """
        Send a test email to verify configuration.
        
        Args:
            recipient_email: Email address to send test to
            
        Returns:
            True if test email sent successfully
        """
        try:
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = recipient_email
            msg['Subject'] = "M&A Research Bot - Test Email"
            
            body = """This is a test email from the M&A Research Bot.

If you received this email, your email configuration is working correctly.

The bot is ready to send M&A research reports.

Best regards,
M&A Research Bot"""
            
            msg.attach(MIMEText(body, 'plain'))
            
            return self._send_email(msg, recipient_email)
            
        except Exception as e:
            self.logger.error(f"Failed to send test email: {e}")
            return False
    
    def get_email_config_status(self) -> Dict:
        """
        Get status of email configuration.
        
        Returns:
            Dictionary with configuration status
        """
        status = {
            'smtp_server_configured': bool(self.smtp_server),
            'sender_email_configured': bool(self.sender_email),
            'password_configured': bool(self.sender_password and 
                                      self.sender_password not in ['your_email_password_or_app_token', 'your_app_password_here']),
            'smtp_port': self.smtp_port,
            'use_tls': self.use_tls
        }
        
        status['fully_configured'] = all([
            status['smtp_server_configured'],
            status['sender_email_configured'],
            status['password_configured']
        ])
        
        return status
