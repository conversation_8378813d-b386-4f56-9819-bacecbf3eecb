#!/usr/bin/env python3
"""
M&A Research Application - Main Entry Point

Comprehensive research application that automatically investigates Merger and 
Acquisition (M&A) news for specified companies, designed to run on Raspberry Pi.

Usage:
    python m_and_a_news.py -e <EMAIL> -c "Microsoft;Activision Blizzard;NVIDIA"
"""

import argparse
import json
import logging
import sys
import os
from datetime import date
from typing import List, Dict, Tuple

# Import our modules
from database import MAResearchDB
from utils import (CompanyNameProcessor, DateCalculator, setup_logging, 
                  create_directories, ConfigValidator)
from data_sources import DataSourceManager
from transcription import TranscriptionManager
from llm_integration import LLMManager
from excel_reporter import ExcelReporter
from email_notifier import EmailNotifier

class MAResearchApp:
    """Main M&A Research Application."""
    
    def __init__(self, config_path: str = "config.json"):
        """Initialize the application."""
        self.config_path = config_path
        self.config = None
        self.logger = None
        self.db = None
        
        # Initialize components
        self.company_processor = None
        self.date_calculator = None
        self.data_source_manager = None
        self.transcription_manager = None
        self.llm_manager = None
        self.excel_reporter = None
        self.email_notifier = None
    
    def load_config(self) -> bool:
        """Load and validate configuration."""
        try:
            if not os.path.exists(self.config_path):
                print(f"Error: Configuration file not found: {self.config_path}")
                return False
            
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
            
            # Validate configuration
            is_valid, errors = ConfigValidator.validate_config(self.config)
            if not is_valid:
                print("Configuration validation errors:")
                for error in errors:
                    print(f"  - {error}")
                return False
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"Error: Invalid JSON in config file: {e}")
            return False
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return False
    
    def initialize_components(self) -> bool:
        """Initialize all application components."""
        try:
            # Set up logging
            self.logger = setup_logging(self.config)
            self.logger.info("Starting M&A Research Application")
            
            # Create directories
            if not create_directories(self.config):
                return False
            
            # Initialize database
            db_path = self.config.get('database_config', {}).get('db_path', 'ma_research.db')
            self.db = MAResearchDB(db_path)
            
            # Initialize processors
            self.company_processor = CompanyNameProcessor()
            self.date_calculator = DateCalculator()
            
            # Initialize managers
            self.data_source_manager = DataSourceManager(self.config)
            self.transcription_manager = TranscriptionManager(self.config)
            self.llm_manager = LLMManager(self.config)
            self.excel_reporter = ExcelReporter(self.config)
            self.email_notifier = EmailNotifier(self.config)
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Component initialization failed: {e}")
            else:
                print(f"Component initialization failed: {e}")
            return False
    
    def run_research(self, companies: List[str], recipient_email: str) -> bool:
        """
        Run the complete M&A research process.
        
        Args:
            companies: List of company names to research
            recipient_email: Email address to send report to
            
        Returns:
            True if research completed successfully
        """
        try:
            self.logger.info(f"Starting research for {len(companies)} companies")
            
            # Process company names (case-insensitive)
            company_name_map = self.company_processor.normalize_company_names(companies)
            self.logger.info(f"Normalized company names: {list(company_name_map.keys())}")
            
            # Calculate date range for search
            last_run_date = self.db.get_last_successful_run_date()
            initial_lookback = self.config.get('general_config', {}).get('initial_lookback_days', 7)
            start_date, end_date = self.date_calculator.calculate_search_date_range(
                last_run_date, initial_lookback
            )
            
            self.logger.info(f"Searching for content from {start_date} to {end_date}")
            
            # Collect all content for all companies
            company_content_map = {}
            company_links_map = {}
            
            for company_lower, company_original in company_name_map.items():
                self.logger.info(f"Researching {company_original}")
                
                # Search all data sources
                content_items = self.data_source_manager.search_all_sources(
                    company_original, start_date, end_date
                )
                
                # Process multimedia content (transcription)
                processed_content = []
                source_links = []
                
                for item in content_items:
                    # Check if we've already processed this content
                    content_hash = self.db.generate_content_hash(
                        item.get('content', '') + item.get('title', ''),
                        item.get('url', '')
                    )
                    
                    if self.db.is_content_processed(content_hash):
                        self.logger.debug(f"Skipping already processed content: {item.get('title', 'Unknown')}")
                        continue
                    
                    # Transcribe multimedia content if needed
                    if item.get('source_type') in ['youtube_video', 'podcast_episode']:
                        transcript = self.transcription_manager.transcribe_content(item)
                        if transcript:
                            # Filter transcript for relevant content
                            relevant_transcript = self.transcription_manager.filter_relevant_transcript(
                                transcript, [company_original]
                            )
                            if relevant_transcript:
                                item['transcript'] = relevant_transcript
                                item['content'] = relevant_transcript  # Use transcript as content
                    
                    # Only include items with meaningful content
                    if item.get('content') or item.get('transcript'):
                        processed_content.append(item)
                        source_links.append(item.get('url', ''))
                        
                        # Record as processed
                        self.db.record_processed_content(
                            content_hash,
                            company_lower,
                            item.get('source_type', 'unknown'),
                            item.get('url', ''),
                            start_date,  # Use search start date as publication date
                            item.get('title', '')
                        )
                
                company_content_map[company_lower] = processed_content
                company_links_map[company_lower] = source_links
                
                self.logger.info(f"Found {len(processed_content)} relevant items for {company_original}")
            
            # Generate summaries using LLM
            self.logger.info("Generating summaries using LLM")
            company_summaries = self.llm_manager.generate_summaries(company_content_map)
            
            # Create Excel report
            self.logger.info("Creating Excel report")
            excel_file_path = self.excel_reporter.create_report(
                company_summaries, company_links_map, company_name_map
            )
            
            # Add statistics to report
            stats = self.excel_reporter.create_summary_statistics(
                company_summaries, company_links_map
            )
            self.excel_reporter.add_statistics_sheet(excel_file_path, stats)
            
            # Validate report
            if not self.excel_reporter.validate_report(excel_file_path):
                self.logger.error("Excel report validation failed")
                return False
            
            # Send email notification
            self.logger.info("Sending email notification")
            email_sent = self.email_notifier.send_report(
                recipient_email, excel_file_path, companies, stats
            )
            
            if not email_sent:
                self.logger.error("Failed to send email notification")
                return False
            
            # Record successful run
            self.db.record_successful_run(companies, recipient_email, excel_file_path)
            
            # Cleanup old records
            cleanup_days = self.config.get('database_config', {}).get('cleanup_days', 90)
            self.db.cleanup_old_records(cleanup_days)
            
            self.logger.info("M&A research completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Research process failed: {e}")
            return False

def parse_arguments() -> Tuple[str, List[str]]:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='M&A Research Application - Automatically research merger and acquisition news',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python m_and_a_news.py -e <EMAIL> -c "Microsoft;Activision Blizzard"
  python m_and_a_news.py --email <EMAIL> --companies "NVIDIA;Intel;AMD"
        """
    )
    
    parser.add_argument(
        '-e', '--email',
        required=True,
        help='Recipient email address for the research report'
    )
    
    parser.add_argument(
        '-c', '--companies',
        required=True,
        help='Semicolon-separated list of company names to research (e.g., "Microsoft;NVIDIA;Intel")'
    )
    
    parser.add_argument(
        '--config',
        default='config.json',
        help='Path to configuration file (default: config.json)'
    )
    
    parser.add_argument(
        '--test-email',
        action='store_true',
        help='Send a test email to verify email configuration'
    )
    
    parser.add_argument(
        '--test-llm',
        action='store_true',
        help='Test LLM connection'
    )
    
    args = parser.parse_args()
    
    # Parse companies list
    companies = [company.strip() for company in args.companies.split(';') if company.strip()]
    
    if not companies:
        parser.error("No valid companies provided. Use semicolon-separated format: 'Company1;Company2'")
    
    return args.email, companies, args.config, args.test_email, args.test_llm

def main():
    """Main application entry point."""
    try:
        # Parse command line arguments
        recipient_email, companies, config_path, test_email, test_llm = parse_arguments()
        
        # Initialize application
        app = MAResearchApp(config_path)
        
        # Load configuration
        if not app.load_config():
            sys.exit(1)
        
        # Initialize components
        if not app.initialize_components():
            sys.exit(1)
        
        # Handle test modes
        if test_email:
            print("Testing email configuration...")
            if app.email_notifier.test_email_connection():
                print("✓ Email connection successful")
                if app.email_notifier.send_test_email(recipient_email):
                    print("✓ Test email sent successfully")
                else:
                    print("✗ Failed to send test email")
                    sys.exit(1)
            else:
                print("✗ Email connection failed")
                sys.exit(1)
            return
        
        if test_llm:
            print("Testing LLM connection...")
            if app.llm_manager.test_connection():
                print("✓ LLM connection successful")
            else:
                print("✗ LLM connection failed")
                sys.exit(1)
            return
        
        # Run the research process
        print(f"Starting M&A research for {len(companies)} companies...")
        print(f"Companies: {', '.join(companies)}")
        print(f"Report will be sent to: {recipient_email}")
        
        success = app.run_research(companies, recipient_email)
        
        if success:
            print("✓ M&A research completed successfully!")
            print("✓ Excel report generated and emailed")
        else:
            print("✗ M&A research failed - check logs for details")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nResearch interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
