"""
Scheduler module for M&A research application.
Handles automated scheduling of research runs.
"""

import schedule
import time
import logging
import subprocess
import sys
from datetime import datetime, time as dt_time
from typing import Dict, List, Optional
import json

class MAResearchScheduler:
    """Handles scheduling of M&A research runs."""
    
    def __init__(self, config: Dict):
        self.config = config.get('scheduler_config', {})
        self.enabled = self.config.get('enabled', False)
        self.schedule_type = self.config.get('schedule_type', 'daily')
        self.run_time = self.config.get('run_time', '09:00')
        self.days_of_week = self.config.get('days_of_week', ['monday', 'wednesday', 'friday'])
        self.timezone = self.config.get('timezone', 'UTC')
        
        self.logger = logging.getLogger(__name__)
        
        # Default parameters for scheduled runs
        self.default_email = None
        self.default_companies = []
        
        # Parse run time
        try:
            hour, minute = map(int, self.run_time.split(':'))
            self.run_time_obj = dt_time(hour, minute)
        except ValueError:
            self.logger.error(f"Invalid run_time format: {self.run_time}")
            self.run_time_obj = dt_time(9, 0)  # Default to 9:00 AM
    
    def set_default_parameters(self, email: str, companies: List[str]):
        """
        Set default parameters for scheduled runs.
        
        Args:
            email: Default recipient email
            companies: Default list of companies to research
        """
        self.default_email = email
        self.default_companies = companies
        self.logger.info(f"Set default parameters: {email}, {len(companies)} companies")
    
    def setup_schedule(self) -> bool:
        """
        Set up the research schedule based on configuration.
        
        Returns:
            True if schedule set up successfully
        """
        if not self.enabled:
            self.logger.info("Scheduler is disabled")
            return True
        
        if not self.default_email or not self.default_companies:
            self.logger.error("Default email and companies must be set before scheduling")
            return False
        
        try:
            # Clear any existing schedule
            schedule.clear()
            
            if self.schedule_type == 'daily':
                schedule.every().day.at(self.run_time).do(self._run_research_job)
                self.logger.info(f"Scheduled daily research at {self.run_time}")
                
            elif self.schedule_type == 'weekly':
                for day in self.days_of_week:
                    day_method = getattr(schedule.every(), day.lower())
                    day_method.at(self.run_time).do(self._run_research_job)
                    self.logger.info(f"Scheduled research on {day} at {self.run_time}")
            
            else:
                self.logger.error(f"Unknown schedule type: {self.schedule_type}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to setup schedule: {e}")
            return False
    
    def _run_research_job(self):
        """Execute the research job."""
        try:
            self.logger.info("Starting scheduled M&A research")
            
            # Prepare command
            companies_str = ';'.join(self.default_companies)
            cmd = [
                sys.executable,
                'm_and_a_news.py',
                '-e', self.default_email,
                '-c', companies_str
            ]
            
            # Run the research
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=3600  # 1 hour timeout
            )
            
            if result.returncode == 0:
                self.logger.info("Scheduled research completed successfully")
            else:
                self.logger.error(f"Scheduled research failed: {result.stderr}")
            
        except subprocess.TimeoutExpired:
            self.logger.error("Scheduled research timed out")
        except Exception as e:
            self.logger.error(f"Scheduled research error: {e}")
    
    def run_scheduler(self):
        """
        Run the scheduler loop.
        This method blocks and runs indefinitely.
        """
        if not self.enabled:
            self.logger.info("Scheduler is disabled, exiting")
            return
        
        self.logger.info("Starting scheduler loop")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            self.logger.info("Scheduler stopped by user")
        except Exception as e:
            self.logger.error(f"Scheduler error: {e}")
    
    def get_next_run_time(self) -> Optional[datetime]:
        """
        Get the next scheduled run time.
        
        Returns:
            Next run datetime or None if no jobs scheduled
        """
        if not schedule.jobs:
            return None
        
        return schedule.next_run()
    
    def get_schedule_info(self) -> Dict:
        """
        Get information about the current schedule.
        
        Returns:
            Dictionary with schedule information
        """
        return {
            'enabled': self.enabled,
            'schedule_type': self.schedule_type,
            'run_time': self.run_time,
            'days_of_week': self.days_of_week if self.schedule_type == 'weekly' else None,
            'timezone': self.timezone,
            'next_run': self.get_next_run_time().isoformat() if self.get_next_run_time() else None,
            'total_jobs': len(schedule.jobs),
            'default_email': self.default_email,
            'default_companies_count': len(self.default_companies) if self.default_companies else 0
        }

class CronScheduler:
    """Alternative scheduler using system cron (Linux/macOS)."""
    
    def __init__(self, config: Dict):
        self.config = config.get('scheduler_config', {})
        self.logger = logging.getLogger(__name__)
    
    def install_cron_job(self, email: str, companies: List[str], 
                        script_path: str = None) -> bool:
        """
        Install a cron job for automated research.
        
        Args:
            email: Recipient email address
            companies: List of companies to research
            script_path: Path to the main script
            
        Returns:
            True if cron job installed successfully
        """
        try:
            import crontab
            
            if not script_path:
                script_path = 'm_and_a_news.py'
            
            # Create cron command
            companies_str = ';'.join(companies)
            command = f'cd {os.getcwd()} && python {script_path} -e {email} -c "{companies_str}"'
            
            # Parse schedule
            schedule_type = self.config.get('schedule_type', 'daily')
            run_time = self.config.get('run_time', '09:00')
            
            hour, minute = map(int, run_time.split(':'))
            
            # Create cron entry
            cron = crontab.CronTab(user=True)
            
            # Remove existing jobs for this script
            cron.remove_all(comment='MA_RESEARCH_BOT')
            
            if schedule_type == 'daily':
                job = cron.new(command=command, comment='MA_RESEARCH_BOT')
                job.setall(f'{minute} {hour} * * *')
                
            elif schedule_type == 'weekly':
                days_of_week = self.config.get('days_of_week', ['monday'])
                day_numbers = {
                    'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
                    'thursday': 4, 'friday': 5, 'saturday': 6
                }
                
                for day in days_of_week:
                    if day.lower() in day_numbers:
                        job = cron.new(command=command, comment='MA_RESEARCH_BOT')
                        day_num = day_numbers[day.lower()]
                        job.setall(f'{minute} {hour} * * {day_num}')
            
            # Write cron jobs
            cron.write()
            
            self.logger.info("Cron job installed successfully")
            return True
            
        except ImportError:
            self.logger.error("python-crontab not available for cron scheduling")
            return False
        except Exception as e:
            self.logger.error(f"Failed to install cron job: {e}")
            return False
    
    def remove_cron_jobs(self) -> bool:
        """Remove all M&A research cron jobs."""
        try:
            import crontab
            
            cron = crontab.CronTab(user=True)
            removed_count = cron.remove_all(comment='MA_RESEARCH_BOT')
            cron.write()
            
            self.logger.info(f"Removed {removed_count} cron jobs")
            return True
            
        except ImportError:
            self.logger.error("python-crontab not available")
            return False
        except Exception as e:
            self.logger.error(f"Failed to remove cron jobs: {e}")
            return False
    
    def list_cron_jobs(self) -> List[str]:
        """List all M&A research cron jobs."""
        try:
            import crontab
            
            cron = crontab.CronTab(user=True)
            jobs = []
            
            for job in cron:
                if job.comment == 'MA_RESEARCH_BOT':
                    jobs.append(f"{job.slices} {job.command}")
            
            return jobs
            
        except ImportError:
            self.logger.error("python-crontab not available")
            return []
        except Exception as e:
            self.logger.error(f"Failed to list cron jobs: {e}")
            return []

def create_scheduler(config: Dict) -> MAResearchScheduler:
    """
    Create appropriate scheduler based on configuration and platform.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Scheduler instance
    """
    return MAResearchScheduler(config)
