"""
Database management module for M&A research application.
Handles SQLite database operations for tracking runs and preventing duplicates.
"""

import sqlite3
import hashlib
import json
import logging
from datetime import datetime, date
from typing import List, Dict, Optional, Tuple

class MAResearchDB:
    """Database manager for M&A research tracking."""
    
    def __init__(self, db_path: str = "ma_research.db"):
        """Initialize database connection and create tables if needed."""
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """Create database tables if they don't exist."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Table for tracking successful runs
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS research_runs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        run_date DATE NOT NULL,
                        companies TEXT NOT NULL,
                        email_recipient TEXT NOT NULL,
                        status TEXT NOT NULL,
                        excel_file_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Table for tracking processed content to prevent duplicates
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS processed_content (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        content_hash TEXT UNIQUE NOT NULL,
                        company_name TEXT NOT NULL,
                        source_type TEXT NOT NULL,
                        source_url TEXT,
                        publication_date DATE,
                        title TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Index for faster lookups
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_content_hash 
                    ON processed_content(content_hash)
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_publication_date 
                    ON processed_content(publication_date)
                ''')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except sqlite3.Error as e:
            self.logger.error(f"Database initialization error: {e}")
            raise
    
    def get_last_successful_run_date(self) -> Optional[date]:
        """Get the date of the last successful research run."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT run_date FROM research_runs 
                    WHERE status = 'completed' 
                    ORDER BY run_date DESC 
                    LIMIT 1
                ''')
                result = cursor.fetchone()
                
                if result:
                    return datetime.strptime(result[0], '%Y-%m-%d').date()
                return None
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting last run date: {e}")
            return None
    
    def record_successful_run(self, companies: List[str], email_recipient: str, 
                            excel_file_path: str) -> bool:
        """Record a successful research run."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO research_runs 
                    (run_date, companies, email_recipient, status, excel_file_path)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    date.today().isoformat(),
                    json.dumps(companies),
                    email_recipient,
                    'completed',
                    excel_file_path
                ))
                conn.commit()
                self.logger.info(f"Recorded successful run for {len(companies)} companies")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"Error recording successful run: {e}")
            return False
    
    def generate_content_hash(self, content: str, source_url: str = "") -> str:
        """Generate a unique hash for content to prevent duplicates."""
        combined_content = f"{content}{source_url}"
        return hashlib.sha256(combined_content.encode('utf-8')).hexdigest()
    
    def is_content_processed(self, content_hash: str) -> bool:
        """Check if content has already been processed."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT 1 FROM processed_content 
                    WHERE content_hash = ?
                ''', (content_hash,))
                return cursor.fetchone() is not None
                
        except sqlite3.Error as e:
            self.logger.error(f"Error checking processed content: {e}")
            return False
    
    def record_processed_content(self, content_hash: str, company_name: str,
                               source_type: str, source_url: str = "",
                               publication_date: Optional[date] = None,
                               title: str = "") -> bool:
        """Record processed content to prevent future duplicates."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR IGNORE INTO processed_content 
                    (content_hash, company_name, source_type, source_url, 
                     publication_date, title)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    content_hash,
                    company_name.lower(),  # Store in lowercase for consistency
                    source_type,
                    source_url,
                    publication_date.isoformat() if publication_date else None,
                    title
                ))
                conn.commit()
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"Error recording processed content: {e}")
            return False
    
    def cleanup_old_records(self, days_to_keep: int = 90) -> bool:
        """Clean up old records to prevent database bloat."""
        try:
            cutoff_date = date.today().replace(day=1)  # Keep at least current month
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Clean old processed content
                cursor.execute('''
                    DELETE FROM processed_content 
                    WHERE created_at < datetime('now', '-{} days')
                '''.format(days_to_keep))
                
                deleted_content = cursor.rowcount
                
                # Keep research runs for longer (they're smaller)
                cursor.execute('''
                    DELETE FROM research_runs 
                    WHERE created_at < datetime('now', '-{} days')
                '''.format(days_to_keep * 2))
                
                deleted_runs = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"Cleaned up {deleted_content} content records and {deleted_runs} run records")
                return True
                
        except sqlite3.Error as e:
            self.logger.error(f"Error during cleanup: {e}")
            return False
    
    def get_stats(self) -> Dict:
        """Get database statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM research_runs')
                total_runs = cursor.fetchone()[0]
                
                cursor.execute('SELECT COUNT(*) FROM processed_content')
                total_content = cursor.fetchone()[0]
                
                cursor.execute('''
                    SELECT COUNT(*) FROM research_runs 
                    WHERE status = "completed"
                ''')
                successful_runs = cursor.fetchone()[0]
                
                return {
                    'total_runs': total_runs,
                    'successful_runs': successful_runs,
                    'total_processed_content': total_content,
                    'database_path': self.db_path
                }
                
        except sqlite3.Error as e:
            self.logger.error(f"Error getting stats: {e}")
            return {}
