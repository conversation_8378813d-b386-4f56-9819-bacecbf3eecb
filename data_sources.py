"""
Data sources module for M&A research application.
Handles querying news APIs and multimedia sources for company information.
"""

import requests
import logging
import time
from datetime import date, datetime
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlencode
import json

class NewsAPIClient:
    """Client for NewsAPI.org news data."""
    
    def __init__(self, config: Dict):
        self.config = config['news_api']
        self.api_key = self.config['api_key']
        self.endpoint = self.config['endpoint']
        self.max_articles = self.config.get('max_articles', 50)
        self.logger = logging.getLogger(__name__)
    
    def search_company_news(self, company_name: str, start_date: date, 
                          end_date: date) -> List[Dict]:
        """
        Search for news articles about a company.
        
        Args:
            company_name: Company name to search for
            start_date: Start date for search
            end_date: End date for search
            
        Returns:
            List of article dictionaries
        """
        if self.api_key == 'your_newsapi_key_here':
            self.logger.warning("NewsAPI key not configured, skipping news search")
            return []
        
        params = {
            'q': f'"{company_name}" AND (merger OR acquisition OR "M&A" OR investment)',
            'from': start_date.isoformat(),
            'to': end_date.isoformat(),
            'sortBy': 'publishedAt',
            'pageSize': min(self.max_articles, 100),
            'language': 'en',
            'apiKey': self.api_key
        }
        
        try:
            response = requests.get(self.endpoint, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            articles = data.get('articles', [])
            
            # Filter and format articles
            formatted_articles = []
            for article in articles:
                if self._is_relevant_article(article, company_name):
                    formatted_articles.append({
                        'title': article.get('title', ''),
                        'content': article.get('content', '') or article.get('description', ''),
                        'url': article.get('url', ''),
                        'published_at': article.get('publishedAt', ''),
                        'source': article.get('source', {}).get('name', 'NewsAPI'),
                        'source_type': 'news_article'
                    })
            
            self.logger.info(f"Found {len(formatted_articles)} relevant articles for {company_name}")
            return formatted_articles
            
        except requests.RequestException as e:
            self.logger.error(f"NewsAPI request failed: {e}")
            return []
        except Exception as e:
            self.logger.error(f"NewsAPI processing error: {e}")
            return []
    
    def _is_relevant_article(self, article: Dict, company_name: str) -> bool:
        """Check if article is relevant to M&A research."""
        title = (article.get('title') or '').lower()
        content = (article.get('content') or article.get('description') or '').lower()
        company_lower = company_name.lower()
        
        # Must mention the company
        if company_lower not in title and company_lower not in content:
            return False
        
        # Must have M&A related keywords
        ma_keywords = ['merger', 'acquisition', 'acquire', 'merge', 'buyout', 
                      'takeover', 'investment', 'deal', 'purchase']
        
        combined_text = f"{title} {content}"
        return any(keyword in combined_text for keyword in ma_keywords)

class SerpAPIClient:
    """Client for SerpAPI multimedia search."""
    
    def __init__(self, config: Dict):
        self.config = config['serpapi']
        self.api_key = self.config['api_key']
        self.endpoint = self.config['endpoint']
        self.max_results = self.config.get('max_results', 20)
        self.logger = logging.getLogger(__name__)
    
    def search_youtube_videos(self, company_name: str, start_date: date, 
                            end_date: date) -> List[Dict]:
        """
        Search for YouTube videos mentioning the company.
        
        Args:
            company_name: Company name to search for
            start_date: Start date for search
            end_date: End date for search
            
        Returns:
            List of video dictionaries
        """
        if self.api_key == 'your_serpapi_key_here':
            self.logger.warning("SerpAPI key not configured, skipping video search")
            return []
        
        query = f'{company_name} merger acquisition M&A'
        params = {
            'engine': 'youtube',
            'search_query': query,
            'api_key': self.api_key
        }
        
        try:
            response = requests.get(self.endpoint, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            videos = data.get('video_results', [])
            
            # Filter and format videos
            formatted_videos = []
            for video in videos[:self.max_results]:
                # Check if video is within date range (approximate)
                if self._is_video_in_date_range(video, start_date, end_date):
                    formatted_videos.append({
                        'title': video.get('title', ''),
                        'url': video.get('link', ''),
                        'video_id': self._extract_video_id(video.get('link', '')),
                        'channel': video.get('channel', {}).get('name', ''),
                        'duration': video.get('duration', ''),
                        'published_date': video.get('published_date', ''),
                        'source_type': 'youtube_video'
                    })
            
            self.logger.info(f"Found {len(formatted_videos)} relevant videos for {company_name}")
            return formatted_videos
            
        except requests.RequestException as e:
            self.logger.error(f"SerpAPI request failed: {e}")
            return []
        except Exception as e:
            self.logger.error(f"SerpAPI processing error: {e}")
            return []
    
    def search_podcast_episodes(self, company_name: str, start_date: date, 
                              end_date: date) -> List[Dict]:
        """
        Search for podcast episodes mentioning the company.
        
        Args:
            company_name: Company name to search for
            start_date: Start date for search
            end_date: End date for search
            
        Returns:
            List of podcast episode dictionaries
        """
        # This is a simplified implementation - in practice, you might use
        # specialized podcast APIs like Listen Notes API
        query = f'{company_name} merger acquisition podcast'
        params = {
            'engine': 'google',
            'q': f'{query} site:podcasts.google.com OR site:spotify.com',
            'api_key': self.api_key
        }
        
        try:
            response = requests.get(self.endpoint, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            results = data.get('organic_results', [])
            
            # Filter and format podcast results
            formatted_podcasts = []
            for result in results[:5]:  # Limit podcast results
                if 'podcast' in result.get('title', '').lower():
                    formatted_podcasts.append({
                        'title': result.get('title', ''),
                        'url': result.get('link', ''),
                        'snippet': result.get('snippet', ''),
                        'source_type': 'podcast_episode'
                    })
            
            self.logger.info(f"Found {len(formatted_podcasts)} relevant podcasts for {company_name}")
            return formatted_podcasts
            
        except requests.RequestException as e:
            self.logger.error(f"Podcast search failed: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Podcast search processing error: {e}")
            return []
    
    def _extract_video_id(self, url: str) -> str:
        """Extract YouTube video ID from URL."""
        if 'youtube.com/watch?v=' in url:
            return url.split('watch?v=')[1].split('&')[0]
        elif 'youtu.be/' in url:
            return url.split('youtu.be/')[1].split('?')[0]
        return ''
    
    def _is_video_in_date_range(self, video: Dict, start_date: date, 
                              end_date: date) -> bool:
        """Check if video is within the specified date range."""
        # This is a simplified check - SerpAPI doesn't always provide exact dates
        # In a production system, you'd want more sophisticated date filtering
        published_date = video.get('published_date', '')
        
        # If no date info, include it (better to have false positives)
        if not published_date:
            return True
        
        # Simple keyword-based date filtering
        recent_keywords = ['day ago', 'days ago', 'week ago', 'weeks ago', 'month ago']
        return any(keyword in published_date.lower() for keyword in recent_keywords)

class DataSourceManager:
    """Manages all data sources for M&A research."""
    
    def __init__(self, config: Dict):
        self.config = config['data_source_config']
        self.news_client = NewsAPIClient(self.config)
        self.serp_client = SerpAPIClient(self.config)
        self.logger = logging.getLogger(__name__)
        
        # Rate limiting
        self.request_delay = config.get('general_config', {}).get('request_delay_seconds', 1)
    
    def search_all_sources(self, company_name: str, start_date: date, 
                          end_date: date) -> List[Dict]:
        """
        Search all configured data sources for company information.
        
        Args:
            company_name: Company name to search for
            start_date: Start date for search
            end_date: End date for search
            
        Returns:
            List of all found content items
        """
        all_content = []
        
        # Search news articles
        self.logger.info(f"Searching news articles for {company_name}")
        news_articles = self.news_client.search_company_news(company_name, start_date, end_date)
        all_content.extend(news_articles)
        
        # Rate limiting
        time.sleep(self.request_delay)
        
        # Search YouTube videos if enabled
        multimedia_config = self.config.get('multimedia_sources', {})
        if multimedia_config.get('youtube', {}).get('enabled', True):
            self.logger.info(f"Searching YouTube videos for {company_name}")
            videos = self.serp_client.search_youtube_videos(company_name, start_date, end_date)
            all_content.extend(videos)
            
            time.sleep(self.request_delay)
        
        # Search podcasts if enabled
        if multimedia_config.get('podcasts', {}).get('enabled', True):
            self.logger.info(f"Searching podcasts for {company_name}")
            podcasts = self.serp_client.search_podcast_episodes(company_name, start_date, end_date)
            all_content.extend(podcasts)
            
            time.sleep(self.request_delay)
        
        self.logger.info(f"Found total of {len(all_content)} content items for {company_name}")
        return all_content
